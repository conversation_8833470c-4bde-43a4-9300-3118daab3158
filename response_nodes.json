[{"parameters": {"jsCode": "// Enhanced response processing with Egyptian Arabic support and image handling\nconst agentOutput = $input.first().json.output;\nconst chatId = $('Telegram Trigger').first().json.message.chat.id;\nconst userId = $('Telegram Trigger').first().json.message.from.id;\n\n// Initialize response object\nlet response = {\n  chatId: chatId,\n  userId: userId,\n  text: agentOutput,\n  hasImage: false,\n  imageUrl: null,\n  products: [],\n  shouldSendImage: false\n};\n\n// Check if agent output contains product information or image requests\nconst imageKeywords = ['صورة', 'شوف', 'اعرض', 'image', 'photo', 'picture'];\nconst hasImageRequest = imageKeywords.some(keyword => \n  agentOutput.toLowerCase().includes(keyword.toLowerCase())\n);\n\n// Try to extract product information from agent tools usage\ntry {\n  // Check if there are any tool results in the workflow\n  const toolNodes = ['Get Products Tool', 'Check Product Availability Tool'];\n  let productData = null;\n  \n  for (const toolName of toolNodes) {\n    try {\n      const toolResult = $(toolName).first()?.json;\n      if (toolResult && toolResult.products) {\n        productData = toolResult.products;\n        break;\n      } else if (toolResult && toolResult.id) {\n        // Single product result\n        productData = [toolResult];\n        break;\n      }\n    } catch (e) {\n      // Tool might not have been executed\n      continue;\n    }\n  }\n  \n  if (productData && productData.length > 0) {\n    // Process products and prepare image\n    response.products = productData.map(product => ({\n      ...product,\n      imageUrl: product.imagePath ? \n        `https://chigger-definite-nominally.ngrok-free.app/uploads/${product.imagePath}` : \n        null\n    }));\n    \n    // If agent mentioned products or customer requested images, prepare to send image\n    if (hasImageRequest || response.products.length <= 3) {\n      const firstProductWithImage = response.products.find(p => p.imageUrl);\n      if (firstProductWithImage) {\n        response.hasImage = true;\n        response.imageUrl = firstProductWithImage.imageUrl;\n        response.shouldSendImage = true;\n      }\n    }\n  }\n} catch (error) {\n  console.log('Error processing product data:', error.message);\n}\n\n// Enhanced text processing for better Arabic support\nif (response.text) {\n  // Ensure proper Arabic text formatting\n  response.text = response.text\n    .replace(/\\n\\n+/g, '\\n\\n') // Clean up multiple line breaks\n    .trim();\n  \n  // Add product information to text if available\n  if (response.products.length > 0 && !response.text.includes('جنيه')) {\n    const productInfo = response.products.slice(0, 3).map(product => \n      `• ${product.name} - ${product.price} جنيه${product.description ? ` (${product.description})` : ''}`\n    ).join('\\n');\n    \n    if (productInfo) {\n      response.text += `\\n\\n${productInfo}`;\n    }\n  }\n}\n\n// Return the processed response\nreturn [{ json: response }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-296, -160], "id": "enhanced-response-processor", "name": "Enhanced Response Processor"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-image", "leftValue": "={{ $json.shouldSendImage }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equals"}}], "combinator": "and"}}, "id": "response-router", "name": "Response Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-72, -160]}, {"parameters": {"operation": "sendPhoto", "chatId": "={{ $json.chatId }}", "file": "={{ $json.imageUrl }}", "caption": "={{ $json.text }}", "additionalFields": {"parse_mode": "None"}}, "id": "enhanced-photo-sender", "name": "Enhanced Photo Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [152, -240], "credentials": {"telegramApi": {"id": "lhlw6jdJ57V04zrv", "name": "Telegram account"}}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "additionalFields": {"parse_mode": "None"}}, "id": "enhanced-text-sender", "name": "Enhanced Text Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [152, -80], "credentials": {"telegramApi": {"id": "lhlw6jdJ57V04zrv", "name": "Telegram account"}}}, {"parameters": {"jsCode": "// Error handling and fallback response\nconst chatId = $('Telegram Trigger').first().json.message.chat.id;\nconst errorMessage = 'عذراً، حصل خطأ تقني. جرب تاني بعد شوية أو تواصل معانا للمساعدة. ربنا يخليك اصبر علينا! 🙏';\n\nreturn [{\n  json: {\n    chatId: chatId,\n    text: errorMessage,\n    hasError: true\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [152, 80], "id": "error-handler", "name": "<PERSON><PERSON><PERSON>"}]