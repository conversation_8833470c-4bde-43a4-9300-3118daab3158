# Backend Dev Dockerfile for Gemini CRM using Debian (faster native builds)
FROM node:18-slim

WORKDIR /app

# Install native build tools and Chrome dependencies (Debian version)
RUN apt-get update && apt-get install -y \
  python3 \
  make \
  g++ \
  libcairo2-dev \
  libjpeg-dev \
  libpango1.0-dev \
  libgif-dev \
  libpixman-1-dev \
  libfreetype6-dev \
  git \
  curl \
  wget \
  gnupg \
  ca-certificates \
  procps \
  libxss1 \
  libgconf-2-4 \
  libxrandr2 \
  libasound2 \
  libpangocairo-1.0-0 \
  libatk1.0-0 \
  libcairo-gobject2 \
  libgtk-3-0 \
  libgdk-pixbuf2.0-0 \
  libxcomposite1 \
  libxcursor1 \
  libxdamage1 \
  libxext6 \
  libxfixes3 \
  libxi6 \
  libxrender1 \
  libxtst6 \
  libcups2 \
  libdrm2 \
  libxss1 \
  libgbm1 \
  libnss3 \
  libxrandr2 \
  libasound2 \
  libatspi2.0-0 \
  libdrm2 \
  libxkbcommon0 \
  libxcomposite1 \
  libxdamage1 \
  libxrandr2 \
  libgbm1 \
  libxss1 \
  libasound2 \
  && rm -rf /var/lib/apt/lists/*

# Copy only package files to leverage Docker cache
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy the app
COPY . .

# Install Chrome for Puppeteer
RUN npx puppeteer browsers install chrome

# Ensure uploads folder exists with proper permissions
RUN mkdir -p uploads && chown -R node:node /app

# Switch to node user
USER node

# Expose backend and debug ports
EXPOSE 5000 9229

# Healthcheck via HTTP ping
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5000/health', res => process.exit(res.statusCode === 200 ? 0 : 1))"

# Start backend in dev mode
CMD ["npm", "run", "dev"]
