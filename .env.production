# Production Environment Configuration for Gemini CRM

# Application
NODE_ENV=production
APP_NAME=Gemini CRM
APP_VERSION=1.0.0
APP_URL=http://localhost

# Server Configuration
PORT=5000
HOST=0.0.0.0

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=gemini_crm_prod
DB_USER=postgres
DB_PASSWORD=Reebo@2004
DB_DIALECT=postgres
DB_LOGGING=false

# JWT Configuration
JWT_SECRET=prod-jwt-secret-key-change-this-in-real-production-2024-secure
JWT_EXPIRES_IN=8h
JWT_REFRESH_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost
CORS_CREDENTIALS=true

# Rate Limiting
API_RATE_LIMIT_WINDOW_MS=900000
API_RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# Email Configuration (Production)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Session Configuration
SESSION_SECRET=prod-session-secret-change-this-2024
SESSION_MAX_AGE=28800000

# Logging Configuration
LOG_LEVEL=warn
LOG_FORMAT=combined

# Security Configuration
TRUST_PROXY=true
HELMET_ENABLED=true
SECURITY_HEADERS=true

# Frontend Configuration (Build time)

VITE_API_URL=http://localhost/api
VITE_SOCKET_URL=http://localhost
VITE_APP_NAME=Gemini CRM
VITE_APP_VERSION=1.0.0
VITE_UPLOAD_MAX_SIZE=5242880
VITE_SUPPORTED_LANGUAGES=en,ar
VITE_DEFAULT_LANGUAGE=en

# Production Settings
DEBUG=false
ENABLE_SWAGGER=false
ENABLE_PLAYGROUND=false

# Notification Configuration
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_PUSH_ENABLED=true
NOTIFICATION_SMS_ENABLED=false

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=daily
BACKUP_RETENTION_DAYS=30

# Performance Configuration
CLUSTER_MODE=false
WORKER_PROCESSES=1
MEMORY_LIMIT=1024
CPU_LIMIT=1

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true
PERFORMANCE_MONITORING=true
