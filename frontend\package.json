{"name": "gemini-crm-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Frontend for Gemini CRM - Women's Bag Store Management System", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "axios": "^1.10.0", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^2.7.3", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-i18next": "^13.5.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.7", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "dotenv": "^17.2.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.0"}}