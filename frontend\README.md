# Gemini CRM - Frontend

React frontend application for the Gemini CRM system.

## 🚀 Quick Start

### Docker Setup (Recommended)
```bash
# From project root
./scripts/docker-dev.sh start
# Frontend available at http://localhost:5173
```

### Manual Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## 📚 Complete Documentation

**All comprehensive frontend documentation is located in [`../docs/frontend/`](../docs/frontend/)**

- **[Frontend Development Guide](../docs/frontend/README.md)** - Complete development documentation
- **[Quick Start Guide](../docs/frontend/quick-start.md)** - Detailed setup instructions
- **[Internationalization](../docs/frontend/internationalization.md)** - i18n setup guide

## 🔧 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```
