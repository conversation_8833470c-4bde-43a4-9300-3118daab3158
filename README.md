# Gemini CRM - Women's Bag Store Management System

A comprehensive web-based CRM system designed specifically for a local retail store selling women's bags. Built with modern technologies and featuring role-based access control, inventory management, and WhatsApp integration capabilities.

## 📚 Documentation

**All comprehensive documentation has been moved to the [`docs/`](./docs/) folder.**

For complete setup, deployment, and usage instructions, please visit:

### 🚀 **Quick Start**
- **[Documentation Hub](./docs/README.md)** - Start here for all documentation
- **[Docker Deployment Guide](./docs/deployment/docker-setup.md)** - Recommended deployment method
- **[Manual Setup Guide](./docs/deployment/production-guide.md)** - Manual installation

### 📖 **Key Documentation**
- **[API Documentation](./docs/api/)** - Complete API reference
- **[User Manual](./docs/user/)** - End-user guides
- **[Development Guides](./docs/backend/)** - Backend development
- **[Frontend Guides](./docs/frontend/)** - Frontend development

## 🚀 Quick Start

### Docker Deployment (Recommended)
```bash
# Start development environment
./scripts/docker-dev.sh start

# Start production environment
./scripts/docker-prod.sh start
```

### Manual Setup
```bash
# Install dependencies
npm install

# Start development servers
npm run dev
```

## 📄 License

ISC License - Built by Reebo4000

---

**Status**: ✅ Production Ready
**Version**: 1.0.0
**Last Updated**: January 2025

**📚 For complete documentation, visit [`docs/`](./docs/)**
