{"name": "CRM Customer Service Agent2", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "id": "telegram-trigger-001", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.2, "position": [-1400, -160], "credentials": {"telegramApi": {"id": "lhlw6jdJ57V04zrv", "name": "Telegram account"}}}, {"parameters": {"options": {"maxTokens": 1500, "temperature": 0.8}}, "id": "openai-model-001", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-1064, 64], "credentials": {"openAiApi": {"id": "FSpiQIrRpUZWjkB6", "name": "OpenAi account 3"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Telegram Trigger').item.json.message.chat.id }}", "contextWindowLength": 15}, "id": "memory-001", "name": "Enhanced Conversation Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-840, 144], "typeVersion": 1.3}, {"parameters": {"promptType": "define", "text": "={{ $json.message.text }}", "prompt": {"messages": [{"type": "system", "message": "أنت مساعد مبيعات ودود ومتخصص في متجر إلكتروني. تتحدث باللهجة المصرية بطريقة طبيعية وودودة، كأنك بائع في محل في شارع التحرير أو خان الخليلي.\n\nشخصيتك:\n- ودود ومرحب زي أهل مصر\n- بتستخدم تعبيرات مصرية طبيعية زي \"إزيك\" و\"أهلاً وسهلاً\" و\"ربنا يخليك\"\n- بتساعد العملاء يلاقوا اللي محتاجينه بصبر ومحبة\n- بتسأل أسئلة ذكية عشان تفهم احتياجات العميل\n- بتقترح منتجات مناسبة بناءً على كلام العميل\n\nمهامك الأساسية:\n1. الترحيب بالعملاء بحرارة\n2. فهم احتياجاتهم من خلال أسئلة ذكية\n3. استخدام الأدوات المتاحة لجلب المنتجات المناسبة\n4. اقتراح منتجات مع الصور\n5. مساعدة العملاء في إتمام الطلبات\n6. الإجابة على الاستفسارات بصبر\n\nأمثلة على طريقة كلامك:\n- \"أهلاً وسهلاً! إزيك النهارده؟ عايز أساعدك في إيه؟\"\n- \"حبيبي، عشان أقدر أساعدك أحسن، قولي الشنطة دي هتستخدمها في إيه؟ شغل ولا سفر ولا خروجات؟\"\n- \"ماشي كده، خليني أدور لك على أحسن الاختيارات اللي عندنا\"\n- \"شوف الصورة دي، إيه رأيك فيها؟\"\n\nقواعد مهمة:\n- استخدم الأدوات المتاحة لجلب بيانات المنتجات والعملاء حسب الحاجة\n- اسأل أسئلة توضيحية قبل البحث عن المنتجات\n- تأكد من توفر المنتجات قبل اقتراحها\n- ساعد العملاء في إنشاء طلبات جديدة\n- احتفظ بسياق المحادثة واستخدمه في ردودك\n- عند اقتراح منتجات، اذكر السعر والوصف والمميزات\n\nتذكر: أنت مش مجرد بوت، أنت صديق بيساعد العملاء يلاقوا اللي محتاجينه بأحسن طريقة ممكنة!"}]}, "options": {}}, "id": "ai-agent-001", "name": "Enhanced Egyptian AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-984, -160], "typeVersion": 2.1}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-984, 192], "id": "gemini-model-001", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "d1QGQ1TEL0kRCgG3", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"name": "Get Products", "description": "البحث عن المنتجات المتاحة في المتجر. استخدم هذه الأداة للبحث عن منتجات معينة أو فئات محددة أو منتجات متاحة في المخزون.", "method": "GET", "url": "https://chigger-definite-nominally.ngrok-free.app/api/integration/products", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-Key", "value": "api_key"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "inStock", "value": "={{ $fromAI('inStock', 'Filter by stock availability (true/false)', 'true') }}"}, {"name": "category", "value": "={{ $fromAI('category', 'Product category to filter by (optional)') }}"}, {"name": "search", "value": "={{ $fromAI('search', 'Search term for product name or description (optional)') }}"}, {"name": "minPrice", "value": "={{ $fromAI('minPrice', 'Minimum price filter (optional)') }}"}, {"name": "maxPrice", "value": "={{ $fromAI('maxPrice', 'Maximum price filter (optional)') }}"}]}, "options": {}}, "id": "get-products-tool", "name": "Get Products Tool", "type": "n8n-nodes-base.httpRequest", "position": [-600, -320], "typeVersion": 4.2}, {"parameters": {"name": "Get Customer Info", "description": "الحصول على معلومات العميل وتاريخ طلباته السابقة.", "method": "GET", "url": "https://chigger-definite-nominally.ngrok-free.app/api/integration/customers", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-Key", "value": "api_key"}]}, "sendQuery": true, "queryParameters": {"parameters": [{"name": "telegramId", "value": "={{ $fromAI('telegramId', 'Telegram user ID to search for') }}"}, {"name": "phone", "value": "={{ $fromAI('phone', 'Customer phone number (optional)') }}"}]}, "options": {}}, "id": "get-customer-tool", "name": "Get Customer Tool", "type": "n8n-nodes-base.httpRequest", "position": [-600, -200], "typeVersion": 4.2}, {"parameters": {"name": "Create Order", "description": "إنشاء طلب جديد للعميل عندما يريد شراء منتجات معينة.", "method": "POST", "url": "https://chigger-definite-nominally.ngrok-free.app/api/integration/orders", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-API-Key", "value": "api_key"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "contentType": "json", "jsonBody": "={{ JSON.stringify({\n  customerId: $fromAI('customerId', 'Customer ID or Telegram ID'),\n  customerName: $fromAI('customerName', 'Customer name'),\n  customerPhone: $fromAI('customerPhone', 'Customer phone number'),\n  customerAddress: $fromAI('customerAddress', 'Customer delivery address'),\n  products: $fromAI('products', 'Array of products with id and quantity'),\n  notes: $fromAI('notes', 'Order notes (optional)')\n}) }}", "options": {}}, "id": "create-order-tool", "name": "Create Order Tool", "type": "n8n-nodes-base.httpRequest", "position": [-600, -80], "typeVersion": 4.2}, {"parameters": {"chatId": "={{ $('Telegram Trigger').item.json.message.from.id }}", "text": "={{ $json.output }}", "additionalFields": {"parse_mode": "None"}}, "id": "telegram-response", "name": "Send Response", "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [-400, -160], "credentials": {"telegramApi": {"id": "lhlw6jdJ57V04zrv", "name": "Telegram account"}}}], "connections": {"Telegram Trigger": {"main": [[{"node": "Enhanced Egyptian AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Enhanced Egyptian AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Enhanced Conversation Memory": {"ai_memory": [[{"node": "Enhanced Egyptian AI Agent", "type": "ai_memory", "index": 0}]]}, "Enhanced Egyptian AI Agent": {"main": [[{"node": "Send Response", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Enhanced Egyptian AI Agent", "type": "ai_languageModel", "index": 1}]]}, "Get Products Tool": {"ai_tool": [[{"node": "Enhanced Egyptian AI Agent", "type": "ai_tool", "index": 0}]]}, "Get Customer Tool": {"ai_tool": [[{"node": "Enhanced Egyptian AI Agent", "type": "ai_tool", "index": 1}]]}, "Create Order Tool": {"ai_tool": [[{"node": "Enhanced Egyptian AI Agent", "type": "ai_tool", "index": 2}]]}}}