# Gemini CRM - Documentation Hub

Welcome to the comprehensive documentation for the Gemini CRM system. **All documentation has been centralized in this `docs/` folder** for better organization and easier navigation.

This documentation is organized into logical sections to help you find the information you need quickly.

## 📚 Documentation Structure

### 🔌 [API Documentation](./api/)
Complete API reference and integration guides:
- **[API Endpoints](./api/endpoints.md)** - Complete list of all API endpoints
- **[Authentication](./api/authentication.md)** - JWT authentication and security
- **[Integration API](./api/integration.md)** - External integration endpoints for n8n/WhatsApp

### 🚀 [Deployment Documentation](./deployment/)
Production deployment and infrastructure guides:
- **[Docker Deployment Guide](./deployment/docker-deployment.md)** - Complete Docker deployment (recommended)
- **[Production Guide](./deployment/production-guide.md)** - Complete manual production deployment
- **[Production Checklist](./deployment/production-checklist.md)** - Pre-deployment checklist
- **[Docker Setup](./deployment/docker-setup.md)** - Containerized deployment (quick reference)

### 🎨 [Frontend Documentation](./frontend/)
React frontend development and configuration:
- **[Frontend Overview](./frontend/README.md)** - Frontend architecture and setup
- **[Quick Start Guide](./frontend/quick-start.md)** - Frontend quick start instructions
- **[Internationalization](./frontend/internationalization.md)** - Arabic/English i18n setup

### ⚙️ [Backend Documentation](./backend/)
Node.js backend development and architecture:
- **[Backend Overview](./backend/README.md)** - Backend architecture and setup
- **[Database](./backend/database.md)** - Database schema and migrations
- **[Architecture](./backend/architecture.md)** - System architecture and patterns

### 👥 [User Documentation](./user/)
End-user guides and manuals:
- **[User Manual](./user/user-manual.md)** - Complete user guide
- **[Admin Guide](./user/admin-guide.md)** - Administrator functions

## 🚀 Quick Start

1. **For Developers**: Start with [Backend](./backend/) and [Frontend](./frontend/) documentation
2. **For Docker Deployment**: Use the [Docker Deployment Guide](./deployment/docker-deployment.md) (recommended)
3. **For Manual Deployment**: Follow the [Production Guide](./deployment/production-guide.md)
4. **For API Integration**: Check [API Documentation](./api/)
5. **For End Users**: Read the [User Manual](./user/user-manual.md)

## 🔧 System Overview

The Gemini CRM is a comprehensive Customer Relationship Management system built for a women's bag retail store with the following key features:

### Core Features
- **Customer Management**: Complete customer database with purchase history
- **Product & Inventory Management**: Real-time stock tracking and management
- **Order Management**: Full order lifecycle with automatic inventory updates
- **Analytics & Reporting**: Comprehensive analytics with PDF export capabilities
- **Real-time Notifications**: WebSocket-based notification system
- **Multi-language Support**: Arabic and English with proper RTL support
- **Role-based Access Control**: Admin and staff user roles with different permissions

### Technology Stack
- **Backend**: Node.js, Express.js, PostgreSQL, Sequelize ORM
- **Frontend**: React, Vite, Tailwind CSS, i18next
- **Authentication**: JWT with bcrypt password hashing
- **Real-time**: Socket.io for notifications
- **PDF Generation**: Puppeteer for report exports
- **Security**: Helmet, CORS, rate limiting, input validation

## 📋 Prerequisites

### Development Environment
- Node.js 18+ and npm
- PostgreSQL 12+
- Git for version control

### Production Environment
- Ubuntu 20.04+ LTS or similar Linux distribution
- Docker and Docker Compose (optional but recommended)
- SSL certificate for HTTPS
- Domain name and DNS configuration

## 🛠️ Installation Quick Reference

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd gemini_crm

# Install dependencies
npm run setup

# Configure environment
cp .env.development .env
# Edit .env with your configuration

# Setup database
npm run migrate
npm run seed

# Start development servers
npm run dev
```

### Production Deployment
```bash
# Docker deployment (recommended)
chmod +x docker-setup.sh
./docker-setup.sh
./scripts/docker-prod.sh start

# Manual production setup
chmod +x scripts/setup-production.sh
./scripts/setup-production.sh
```

## 📋 Complete Documentation Index

For a comprehensive index of all documentation files, see **[DOCUMENTATION_INDEX.md](./DOCUMENTATION_INDEX.md)**.

## 📞 Support and Contributing

- **Issues**: Report bugs and feature requests through the project repository
- **Documentation**: Help improve documentation by submitting pull requests
- **Development**: Follow the coding standards and patterns established in the codebase

## 📄 License

ISC License - Built by Reebo4000

---

**Version**: 1.0.0
**Last Updated**: January 2025
**Status**: ✅ Production Ready
**Organization**: ✅ All documentation centralized in docs/ folder

For specific technical details, please refer to the appropriate section documentation linked above.
