
services:
  # PostgreSQL Database for Development
  postgres:
    image: postgres:15-alpine
    container_name: gemini-crm-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: gemini_crm
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: Reebo@2004
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
      - ./backend/database-init:/docker-entrypoint-initdb.d
    networks:
      - gemini-crm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d gemini_crm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Service for Development
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: gemini-crm-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: gemini_crm
      DB_USER: postgres
      DB_PASSWORD: Reebo@2004
      JWT_SECRET: dev-jwt-secret-key-change-in-production-2024
      JWT_EXPIRES_IN: 24h
      API_RATE_LIMIT_WINDOW_MS: 900000
      API_RATE_LIMIT_MAX_REQUESTS: 1000
      CORS_ORIGIN: http://localhost:3000,http://localhost:5173
      UPLOAD_PATH: /app/uploads
      MAX_FILE_SIZE: 10485760
      ALLOWED_FILE_TYPES: image/jpeg,image/png,image/gif,image/webp
      EMAIL_HOST: smtp.gmail.com
      EMAIL_PORT: 587
      EMAIL_SECURE: false
      EMAIL_USER: <EMAIL>
      EMAIL_PASS: your-app-password
      NOTIFICATION_EMAIL_FROM: <EMAIL>
      INTEGRATION_API_KEY: gemini_crm_integration_key_2024
    ports:
      - "5000:5000"
      - "9229:9229"  # Debug port
    volumes:
      - ./backend:/app
      - /app/node_modules
      - backend_uploads_dev:/app/uploads
    networks:
      - gemini-crm-network
    depends_on:
      postgres:
        condition: service_healthy
    command: ["npm", "run", "dev"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service for Development
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: gemini-crm-frontend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:5000/api
      VITE_SOCKET_URL: http://localhost:5000
      VITE_APP_NAME: Gemini CRM
      VITE_APP_VERSION: 1.0.0
      VITE_UPLOAD_MAX_SIZE: 10485760
      VITE_SUPPORTED_LANGUAGES: en,ar
      VITE_DEFAULT_LANGUAGE: en
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - gemini-crm-network
    depends_on:
      - backend
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5173/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Session Storage and Caching (Development)
  redis:
    image: redis:7-alpine
    container_name: gemini-crm-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data_dev:/data
    networks:
      - gemini-crm-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # pgAdmin for Database Management (Development)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: gemini-crm-pgadmin-dev
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data_dev:/var/lib/pgadmin
    networks:
      - gemini-crm-network
    depends_on:
      - postgres

  ngrok:
    image: ngrok/ngrok:latest
    container_name: ngrok-crm
    restart: unless-stopped
    command: http --domain=chigger-definite-nominally.ngrok-free.app frontend:5173
    environment:
      - NGROK_AUTHTOKEN=1oQtwzGEiDAEAB80xUK6Y1iOjSf_815buSpvA7h423ej2uJQ
    networks:
      - gemini-crm-network
volumes:
  postgres_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  pgadmin_data_dev:
    driver: local
  backend_uploads_dev:
    driver: local




networks:
  gemini-crm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
