{
  "name": "Gemini CRM AI Customer Service Agent - Arabic Telegram Bot",
  "nodes": [
    {
      "parameters": {
        "updates": [
          "message"
        ]
      },
      "id": "telegram-trigger",
      "name": "Telegram Trigger",
      "type": "n8n-nodes-base.telegramTrigger",
      "typeVersion": 1.2,
      "position": [
        240,
        300
      ],
      "webhookId": "telegram-webhook-crm",
      "credentials": {
        "telegramApi": {
          "id": "telegram-bot-credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// Extract and process incoming Telegram message\nconst message = $input.first().json.message;\nconst chatId = message.chat.id;\nconst userId = message.from.id;\nconst userName = message.from.first_name + ' ' + (message.from.last_name || '');\nconst userPhone = message.contact?.phone_number || null;\nconst messageText = message.text || '';\nconst messageType = message.photo ? 'photo' : message.document ? 'document' : 'text';\n\n// Extract user context\nconst userContext = {\n  telegramId: userId,\n  chatId: chatId,\n  userName: userName.trim(),\n  phone: userPhone,\n  messageText: messageText,\n  messageType: messageType,\n  timestamp: new Date().toISOString(),\n  language: 'ar' // Arabic\n};\n\n// Store conversation context for AI agent\nconst conversationContext = {\n  userId: userId,\n  chatId: chatId,\n  currentMessage: messageText,\n  messageHistory: [], // Will be populated from database\n  customerData: null, // Will be populated from CRM\n  intent: null // Will be determined by AI\n};\n\nreturn {\n  userContext,\n  conversationContext,\n  originalMessage: message\n};"
      },
      "id": "message-processor",
      "name": "Message Processor",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:5000/api/integration/customers",
        "method": "GET",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "X-API-Key",
              "value": "={{ $env.GEMINI_CRM_API_KEY }}"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "phone",
              "value": "={{ $json.userContext.phone }}"
            },
            {
              "name": "telegramId",
              "value": "={{ $json.userContext.telegramId }}"
            }
          ]
        }
      },
      "id": "customer-lookup",
      "name": "Customer Lookup",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        680,
        200
      ],
      "continueOnFail": true
    },
    {
      "parameters": {
        "url": "http://localhost:5000/api/integration/customers",
        "method": "POST",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "X-API-Key",
              "value": "={{ $env.GEMINI_CRM_API_KEY }}"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendBody": true,
        "contentType": "json",
        "jsonBody": "{\n  \"firstName\": \"{{ $json.userContext.userName.split(' ')[0] }}\",\n  \"lastName\": \"{{ $json.userContext.userName.split(' ').slice(1).join(' ') || 'غير محدد' }}\",\n  \"phone\": \"{{ $json.userContext.phone || 'telegram_' + $json.userContext.telegramId }}\",\n  \"email\": null,\n  \"address\": \"Telegram User\",\n  \"city\": \"غير محدد\",\n  \"notes\": \"Customer created via Telegram Bot - ID: {{ $json.userContext.telegramId }}\"\n}"
      },
      "id": "create-customer",
      "name": "Create Customer",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        680,
        400
      ],
      "executeOnce": false
    },
    {
      "parameters": {
        "jsCode": "// Merge customer data from lookup or creation\nconst messageData = $input.first().json;\nconst customerLookup = $input.all().find(item => item.json.success && item.json.customer);\nconst customerCreation = $input.all().find(item => item.json.success && item.json.data);\n\nlet customerData = null;\n\nif (customerLookup) {\n  customerData = customerLookup.json.customer;\n} else if (customerCreation) {\n  customerData = customerCreation.json.data;\n}\n\n// Enhanced conversation context with customer data\nconst enhancedContext = {\n  ...messageData.conversationContext,\n  customerData: customerData,\n  isNewCustomer: !customerLookup,\n  customerName: customerData?.firstName + ' ' + (customerData?.lastName || ''),\n  customerId: customerData?.id,\n  totalPurchases: customerData?.totalPurchases || 0,\n  lastOrderDate: customerData?.lastOrderDate || null\n};\n\nreturn {\n  ...messageData,\n  conversationContext: enhancedContext\n};"
      },
      "id": "merge-customer-data",
      "name": "Merge Customer Data",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        900,
        300
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:5000/api/integration/products",
        "method": "GET",
        "sendHeaders": true,
        "headerParameters": {
          "parameters": [
            {
              "name": "X-API-Key",
              "value": "={{ $env.GEMINI_CRM_API_KEY }}"
            },
            {
              "name": "Content-Type",
              "value": "application/json"
            }
          ]
        },
        "sendQuery": true,
        "queryParameters": {
          "parameters": [
            {
              "name": "limit",
              "value": "50"
            },
            {
              "name": "inStock",
              "value": "true"
            }
          ]
        }
      },
      "id": "get-products",
      "name": "Get Products",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.2,
      "position": [
        1120,
        200
      ]
    },
    {
      "parameters": {
        "promptType": "define",
        "text": "أنت مساعد خدمة عملاء ذكي لشركة جيميني للتجارة الإلكترونية. تتحدث العربية بلهجة مصرية ودودة ومهنية.\n\nمعلومات العميل:\n- الاسم: {{ $json.conversationContext.customerName }}\n- رقم العميل: {{ $json.conversationContext.customerId }}\n- إجمالي المشتريات: {{ $json.conversationContext.totalPurchases }} جنيه\n- عميل جديد: {{ $json.conversationContext.isNewCustomer ? 'نعم' : 'لا' }}\n\nالرسالة الحالية: {{ $json.conversationContext.currentMessage }}\n\nالمنتجات المتاحة: {{ JSON.stringify($json.products) }}\n\nمهامك:\n1. الرد بطريقة ودودة ومهنية باللهجة المصرية\n2. مساعدة العميل في استفساراته عن المنتجات\n3. تقديم توصيات مناسبة بناءً على احتياجاته وتاريخ مشترياته\n4. التعامل مع الشكاوى والمشاكل بصبر واحترافية\n5. إذا طلب العميل منتجات معينة، اعرض عليه المنتجات المناسبة مع الأسعار والتفاصيل\n\nقواعد مهمة:\n- استخدم اللهجة المصرية الودودة (مثل: إزيك، أهلاً وسهلاً، حضرتك، إن شاء الله)\n- اذكر اسم العميل في الرد\n- إذا كان عميل جديد، رحب به ترحيباً خاصاً\n- اعرض المساعدة في اختيار المنتجات\n- إذا سأل عن منتج معين، ابحث في قائمة المنتجات المتاحة\n- اذكر الأسعار بالجنيه المصري\n- كن مفيداً ومساعداً دائماً",
        "hasOutputParser": true,
        "outputParser": {
          "type": "structured",
          "jsonSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"response\": {\n      \"type\": \"string\",\n      \"description\": \"الرد النصي للعميل باللهجة المصرية\"\n    },\n    \"intent\": {\n      \"type\": \"string\",\n      \"enum\": [\"greeting\", \"product_inquiry\", \"complaint\", \"order_status\", \"recommendation_request\", \"general_support\"],\n      \"description\": \"نوع الاستفسار\"\n    },\n    \"recommended_products\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\"type\": \"number\"},\n          \"name\": {\"type\": \"string\"},\n          \"price\": {\"type\": \"number\"},\n          \"reason\": {\"type\": \"string\"}\n        }\n      },\n      \"description\": \"قائمة المنتجات الموصى بها إن وجدت\"\n    },\n    \"needs_human_support\": {\n      \"type\": \"boolean\",\n      \"description\": \"هل يحتاج العميل لدعم بشري متخصص\"\n    }\n  },\n  \"required\": [\"response\", \"intent\"]\n}"
      },
      "id": "ai-agent",
      "name": "AI Customer Service Agent",
      "type": "n8n-nodes-langchain.agent",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ],
      "credentials": {
        "openAiApi": {
          "id": "openai-credentials",
          "name": "OpenAI API"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// Process AI agent response and prepare for sending\nconst aiResponse = $input.first().json;\nconst originalData = $input.first().json;\n\n// Parse AI response\nlet parsedResponse;\ntry {\n  parsedResponse = typeof aiResponse.output === 'string' ? JSON.parse(aiResponse.output) : aiResponse.output;\n} catch (error) {\n  // Fallback if parsing fails\n  parsedResponse = {\n    response: aiResponse.output || 'عذراً، حدث خطأ في النظام. يرجى المحاولة مرة أخرى.',\n    intent: 'general_support',\n    recommended_products: [],\n    needs_human_support: false\n  };\n}\n\n// Prepare response data\nconst responseData = {\n  chatId: originalData.userContext.chatId,\n  message: parsedResponse.response,\n  intent: parsedResponse.intent,\n  recommendedProducts: parsedResponse.recommended_products || [],\n  needsHumanSupport: parsedResponse.needs_human_support || false,\n  customerData: originalData.conversationContext.customerData\n};\n\nreturn responseData;"
      },
      "id": "process-ai-response",
      "name": "Process AI Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1560,
        300
      ]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "={{ $json.chatId }}",
        "text": "{{ $json.message }}",
        "additionalFields": {
          "parse_mode": "HTML",
          "disable_web_page_preview": false
        }
      },
      "id": "send-text-response",
      "name": "Send Text Response",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        1780,
        200
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram-bot-credentials",
          "name": "Telegram Bot API"
        }
      }
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "has-recommendations",
              "leftValue": "={{ $json.recommendedProducts.length }}",
              "rightValue": 0,
              "operator": {
                "type": "number",
                "operation": "gt"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "check-recommendations",
      "name": "Check Recommendations",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        1780,
        400
      ]
    },
    {
      "parameters": {
        "jsCode": "// Process recommended products and prepare for display\nconst responseData = $input.first().json;\nconst recommendedProducts = responseData.recommendedProducts || [];\n\nconst productMessages = recommendedProducts.map((product, index) => {\n  const productMessage = `🛍️ <b>${product.name}</b>\\n` +\n    `💰 السعر: ${product.price} جنيه\\n` +\n    `📝 ${product.reason || 'منتج مميز ومناسب لك'}\\n` +\n    `🆔 كود المنتج: ${product.id}`;\n  \n  return {\n    chatId: responseData.chatId,\n    message: productMessage,\n    productId: product.id,\n    productName: product.name,\n    productPrice: product.price\n  };\n});\n\nreturn productMessages;"
      },
      "id": "format-product-recommendations",
      "name": "Format Product Recommendations",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        2000,
        500
      ]
    },
    {
      "parameters": {
        "resource": "message",
        "operation": "sendMessage",
        "chatId": "={{ $json.chatId }}",
        "text": "{{ $json.message }}",
        "additionalFields": {
          "parse_mode": "HTML",
          "reply_markup": {
            "inline_keyboard": [
              [
                {
                  "text": "🛒 أضف للسلة",
                  "callback_data": "add_to_cart_{{ $json.productId }}"
                },
                {
                  "text": "ℹ️ تفاصيل أكثر",
                  "callback_data": "product_details_{{ $json.productId }}"
                }
              ]
            ]
          }
        }
      },
      "id": "send-product-recommendation",
      "name": "Send Product Recommendation",
      "type": "n8n-nodes-base.telegram",
      "typeVersion": 1.2,
      "position": [
        2220,
        500
      ],
      "credentials": {
        "telegramApi": {
          "id": "telegram-bot-credentials",
          "name": "Telegram Bot API"
        }
      }
    }
  ],
  "connections": {
    "Telegram Trigger": {
      "main": [
        [
          {
            "node": "Message Processor",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Message Processor": {
      "main": [
        [
          {
            "node": "Customer Lookup",
            "type": "main",
            "index": 0
          },
          {
            "node": "Create Customer",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Customer Lookup": {
      "main": [
        [
          {
            "node": "Merge Customer Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create Customer": {
      "main": [
        [
          {
            "node": "Merge Customer Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Merge Customer Data": {
      "main": [
        [
          {
            "node": "Get Products",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Products": {
      "main": [
        [
          {
            "node": "AI Customer Service Agent",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Customer Service Agent": {
      "main": [
        [
          {
            "node": "Process AI Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Process AI Response": {
      "main": [
        [
          {
            "node": "Send Text Response",
            "type": "main",
            "index": 0
          },
          {
            "node": "Check Recommendations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Recommendations": {
      "main": [
        [
          {
            "node": "Format Product Recommendations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format Product Recommendations": {
      "main": [
        [
          {
            "node": "Send Product Recommendation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "settings": {
    "executionOrder": "v1"
  },
  "staticData": {},
  "tags": [
    {
      "createdAt": "2024-01-20T10:00:00.000Z",
      "updatedAt": "2024-01-20T10:00:00.000Z",
      "id": "crm-ai-agent",
      "name": "CRM AI Agent"
    }
  ],
  "triggerCount": 1,
  "updatedAt": "2024-01-20T10:00:00.000Z",
  "versionId": "1"
}
