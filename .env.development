# Development Environment Configuration for Gemini CRM

# Application
NODE_ENV=development
APP_NAME=Gemini CRM
APP_VERSION=1.0.0
APP_URL=http://localhost:5173

# Server Configuration
PORT=5000
HOST=0.0.0.0

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=gemini_crm
DB_USER=postgres
DB_PASSWORD=Reebo@2004
DB_DIALECT=postgres
DB_LOGGING=true

# JWT Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production-2024
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:5173,http://localhost:8080
CORS_CREDENTIALS=true

# Rate Limiting
API_RATE_LIMIT_WINDOW_MS=900000
API_RATE_LIMIT_MAX_REQUESTS=1000

# File Upload Configuration
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,image/svg+xml

# Email Configuration (Development)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Session Configuration
SESSION_SECRET=dev-session-secret-2024
SESSION_MAX_AGE=86400000

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=combined

# Security Configuration
TRUST_PROXY=false
HELMET_ENABLED=true
SECURITY_HEADERS=true

# Frontend Configuration

VITE_API_URL=http://localhost:5000/api
VITE_SOCKET_URL=http://localhost:5000
VITE_APP_NAME=Gemini CRM
VITE_APP_VERSION=1.0.0
VITE_UPLOAD_MAX_SIZE=10485760
VITE_SUPPORTED_LANGUAGES=en,ar
VITE_DEFAULT_LANGUAGE=en

# Development Tools
DEBUG=true
ENABLE_SWAGGER=true
ENABLE_PLAYGROUND=true

# Notification Configuration
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_PUSH_ENABLED=false
NOTIFICATION_SMS_ENABLED=false

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_INTERVAL=daily
BACKUP_RETENTION_DAYS=7
